# AL-SALAMAT - موقع شركة زجاج السيارات

موقع ويب لشركة السلامة لزجاج السيارات مع نظام تسجيل الدخول باستخدام Firebase.

## المميزات

- 🎨 تصميم عصري ومتجاوب
- 🔐 نظام تسجيل دخول وإنشاء حساب باستخدام Firebase
- 📱 شريط جانبي للموبايل
- 🖼️ معرض صور للخدمات
- 🏢 عرض الفروع مع معلومات الاتصال

## التقنيات المستخدمة

- HTML5
- CSS3
- JavaScript (ES6+)
- Firebase Authentication
- Firebase Firestore

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
npm install
```

### 2. تشغيل الموقع

```bash
npm start
```

أو

```bash
npm run dev
```

سيتم فتح الموقع على: `http://localhost:3000`

## إعداد Firebase

الموقع مُعد مسبقاً للعمل مع Firebase باستخدام المعلومات التالية:

- **Project ID:** al-salamat
- **Auth Domain:** al-salamat.firebaseapp.com
- **Database URL:** https://al-salamat-default-rtdb.firebaseio.com

## الملفات الرئيسية

- `index.html` - الصفحة الرئيسية
- `login.html` - صفحة تسجيل الدخول وإنشاء الحساب
- `styles.css` - ملف التصميم
- `firebase-config.js` - إعدادات Firebase
- `package.json` - معلومات المشروع والمتطلبات

## الوظائف

### تسجيل الدخول
- تسجيل دخول بالبريد الإلكتروني وكلمة المرور
- رسائل خطأ باللغة العربية
- حفظ بيانات المستخدم في localStorage

### إنشاء حساب
- إنشاء حساب جديد بالبيانات الشخصية
- حفظ البيانات في Firestore
- التحقق من تطابق كلمة المرور

### الأمان
- التحقق من صحة البيانات
- رسائل خطأ واضحة
- حماية من الهجمات الشائعة

## المتصفحات المدعومة

- Chrome (الأحدث)
- Firefox (الأحدث)
- Safari (الأحدث)
- Edge (الأحدث)

## الدعم

للدعم الفني أو الاستفسارات، يرجى التواصل مع فريق التطوير.
