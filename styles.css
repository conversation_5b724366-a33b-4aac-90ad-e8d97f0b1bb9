/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    position: relative;
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    font-family: 'Arial', sans-serif;
    letter-spacing: 1px;
}

.login-link {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    color: white !important;
    border-radius: 5px !important;
    font-weight: 600 !important;
    box-shadow: 0 3px 15px rgba(102, 126, 234, 0.3) !important;
}

.login-link:hover {
    background: linear-gradient(45deg, #764ba2, #667eea) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4) !important;
}

.login-link.logged-in {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    pointer-events: none !important;
}

.login-link.logged-in:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    transform: none !important;
}

/* User Menu Dropdown */
.user-menu {
    position: relative;
    display: inline-block;
}

.user-menu-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    border-radius: 5px !important;
    font-weight: 600 !important;
    box-shadow: 0 3px 15px rgba(76, 175, 80, 0.3) !important;
    text-decoration: none !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: none !important;
    font-size: inherit !important;
}

.user-menu-btn:hover {
    background: linear-gradient(45deg, #45a049, #4CAF50) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 20px rgba(76, 175, 80, 0.4) !important;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-item {
    display: block;
    padding: 1rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.user-dropdown-item:first-child {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.user-dropdown-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.user-dropdown-item:hover {
    background: #f5f5f5;
}

.user-dropdown-item.status {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    font-weight: 600;
    pointer-events: none;
}

.user-dropdown-item.logout {
    color: #f44336;
    font-weight: 500;
}

.user-dropdown-item.logout:hover {
    background: rgba(244, 67, 54, 0.1);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-links a:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-btn span {
    width: 25px;
    height: 3px;
    background: #667eea;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    z-index: 2000;
    padding: 0;
}

.sidebar.active {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
    letter-spacing: 1px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 2rem;
    color: #667eea;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.sidebar-nav a {
    display: block;
    padding: 1.2rem 1.5rem;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.sidebar-nav a:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
}

.sidebar-login-link {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    color: white !important;
    font-weight: 600 !important;
    text-align: center !important;
    margin: 1rem !important;
    border-radius: 5px !important;
}

.sidebar-login-link:hover {
    background: linear-gradient(45deg, #764ba2, #667eea) !important;
    transform: scale(1.02) !important;
}

.sidebar-login-link.logged-in {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    pointer-events: none !important;
}

.sidebar-login-link.logged-in:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    transform: none !important;
}

/* Sidebar User Menu */
.sidebar-user-menu {
    margin: 1rem;
}

.sidebar-user-status {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    font-weight: 600 !important;
    text-align: center !important;
    padding: 1rem !important;
    border-radius: 5px !important;
    margin-bottom: 0.5rem !important;
    pointer-events: none !important;
}

.sidebar-logout-btn {
    background: rgba(244, 67, 54, 0.1) !important;
    color: #f44336 !important;
    font-weight: 500 !important;
    text-align: center !important;
    padding: 1rem !important;
    border-radius: 5px !important;
    text-decoration: none !important;
    display: block !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
}

.sidebar-logout-btn:hover {
    background: rgba(244, 67, 54, 0.2) !important;
    transform: scale(1.02) !important;
}

/* Overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1500;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Main Content Styles */
main {
    margin-top: 100px;
    padding: 2rem 0;
}

.hero-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    letter-spacing: 2px;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #764ba2;
    margin-bottom: 2rem;
}

.company-image {
    width: 100%;
    max-width: 600px;
    height: 400px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
    margin: 2rem 0;
}

/* Gallery Section Styles */
.gallery-section {
    margin-top: 2rem;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.gallery-item {
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.02);
}

/* About Section Styles */
.about-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.about-title {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 2rem;
    text-align: center;
}

.about-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #555;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

/* Branches Section Styles */
.branches-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.branches-title {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 2rem;
    text-align: center;
}

.branches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.branch-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.branch-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.branch-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.branch-card a {
    color: white;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    margin-top: 1rem;
    padding: 0.5rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    transition: all 0.3s ease;
}

.branch-card a:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Footer Styles */
footer {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Hide desktop navigation and show mobile menu button */
    .desktop-nav {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    /* Reset nav positioning for mobile */
    nav {
        justify-content: space-between;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .branches-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .gallery-image {
        height: 150px;
    }

    .logo {
        font-size: 1.5rem;
    }



    .about-title,
    .branches-title {
        font-size: 2rem;
    }

    /* Adjust sidebar for smaller screens */
    .sidebar {
        width: 280px;
        right: -280px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .about-text {
        font-size: 1rem;
    }

    .company-image {
        height: 250px;
    }

    .gallery-image {
        height: 120px;
    }

    .gallery-section {
        margin-top: 1.5rem;
    }

    /* Adjust sidebar for very small screens */
    .sidebar {
        width: 250px;
        right: -250px;
    }

    .sidebar-header {
        padding: 1rem;
    }

    .sidebar-nav a {
        padding: 1rem;
    }
}
