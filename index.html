<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AL-SALAMAT - شركة زجاج السيارات</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">AL-SALAMAT</div>

            <ul class="nav-links desktop-nav">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#about">من نحن</a></li>
                <li><a href="#branches">الفروع</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>

            <div class="nav-right">
                <button class="mobile-menu-btn" onclick="toggleSidebar()">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <div id="login-menu-item">
                    <a href="login.html" class="login-link">التسجيل</a>
                    <div class="user-menu" id="user-menu" style="display: none;">
                        <button class="user-menu-btn" onclick="toggleUserDropdown()">تم التسجيل ▼</button>
                        <div class="user-dropdown" id="user-dropdown">
                            <div class="user-dropdown-item status">تم التسجيل</div>
                            <div class="user-dropdown-item logout" onclick="logout()">تسجيل الخروج</div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Mobile Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">AL-SALAMAT</div>
            <button class="close-btn" onclick="toggleSidebar()">&times;</button>
        </div>
        <div id="sidebar-login-section">
            <a href="login.html" class="sidebar-login-link">التسجيل</a>
        </div>
        <div class="sidebar-user-menu" id="sidebar-user-menu" style="display: none;">
            <div class="sidebar-user-status">تم التسجيل</div>
            <div class="sidebar-logout-btn" onclick="logout()">تسجيل الخروج</div>
        </div>
        <ul class="sidebar-nav">
            <li><a href="#home" onclick="toggleSidebar()">الرئيسية</a></li>
            <li><a href="#about" onclick="toggleSidebar()">من نحن</a></li>
            <li><a href="#branches" onclick="toggleSidebar()">الفروع</a></li>
            <li><a href="#contact" onclick="toggleSidebar()">اتصل بنا</a></li>
        </ul>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay" onclick="toggleSidebar()"></div>

    <main class="container">
        <section id="home" class="hero-section">
            <h1 class="hero-title">AL-SALAMAT</h1>
            <h2 class="hero-subtitle">رائدة في زجاج السيارات</h2>
            <img src="img/zogag.jpg" alt="زجاج السيارات" class="company-image">

            <!-- Gallery Images -->
            <div class="gallery-section">
                <div class="gallery-grid">
                    <div class="gallery-item">
                        <img src="img/car2.png" alt="خدمات زجاج السيارات" class="gallery-image">
                    </div>
                    <div class="gallery-item">
                        <img src="img/car4.png" alt="تركيب زجاج السيارات" class="gallery-image">
                    </div>
                    <div class="gallery-item">
                        <img src="img/car6.png" alt="إصلاح زجاج السيارات" class="gallery-image">
                    </div>
                </div>
            </div>
        </section>

        <section id="about" class="about-section">
            <h2 class="about-title">من نحن</h2>
            <p class="about-text">
                نحن شركة رائدة في استيراد وتوزيع وتركيب وتصليح وقص زجاج السيارات، نخدم المجتمع المحلي ونسعى دائماً لأن تكون لنا لمستنا المميزة فيه. 
                نفتخر بخبرتنا الواسعة وجودة خدماتنا العالية، ونلتزم بتقديم أفضل الحلول لعملائنا الكرام بأحدث التقنيات والمعدات المتطورة.
            </p>
        </section>

        <section id="branches" class="branches-section">
            <h2 class="branches-title">فروعنا</h2>
            <div class="branches-grid">
                <div class="branch-card">
                    <h3>الفرع الأول - المدينة</h3>
                    <p>شارع الملك فهد، المدينة المنورة</p>
                    <a href="tel:+966501234567">اتصل بنا</a>
                </div>
                <div class="branch-card">
                    <h3>الفرع الثاني - جدة</h3>
                    <p>طريق الملك عبدالعزيز، جدة</p>
                    <a href="tel:+966507654321">اتصل بنا</a>
                </div>
                <div class="branch-card">
                    <h3>الفرع الثالث - الرياض</h3>
                    <p>شارع العليا، الرياض</p>
                    <a href="tel:+966509876543">اتصل بنا</a>
                </div>
                <div class="branch-card">
                    <h3>الفرع الرابع - الدمام</h3>
                    <p>الكورنيش الشرقي، الدمام</p>
                    <a href="tel:+966502468135">اتصل بنا</a>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 شركة AL-SALAMAT لزجاج السيارات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');

            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        }

        // Close sidebar when clicking on a link
        document.querySelectorAll('.sidebar-nav a').forEach(link => {
            link.addEventListener('click', () => {
                toggleSidebar();
            });
        });

        // Toggle user dropdown menu
        function toggleUserDropdown() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('user-menu');
            const dropdown = document.getElementById('user-dropdown');

            if (userMenu && !userMenu.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Check if user is logged in and update login button
        function checkLoginStatus() {
            const user = localStorage.getItem('user');

            if (user) {
                const userData = JSON.parse(user);
                const userName = userData.displayName || 'المستخدم';

                // Show user menu in desktop nav
                document.querySelector('.login-link').style.display = 'none';
                document.getElementById('user-menu').style.display = 'inline-block';

                // Show user menu in sidebar
                document.getElementById('sidebar-login-section').style.display = 'none';
                document.getElementById('sidebar-user-menu').style.display = 'block';
            } else {
                // Show login links
                document.querySelector('.login-link').style.display = 'inline-block';
                document.getElementById('user-menu').style.display = 'none';

                // Show login link in sidebar
                document.getElementById('sidebar-login-section').style.display = 'block';
                document.getElementById('sidebar-user-menu').style.display = 'none';
            }
        }

        // Logout function
        function logout() {
            if (confirm('هل تريد تسجيل الخروج؟')) {
                localStorage.removeItem('user');

                // Hide user menus and show login links
                document.querySelector('.login-link').style.display = 'inline-block';
                document.getElementById('user-menu').style.display = 'none';

                // Hide sidebar user menu and show login link
                document.getElementById('sidebar-login-section').style.display = 'block';
                document.getElementById('sidebar-user-menu').style.display = 'none';

                // Close dropdown if open
                document.getElementById('user-dropdown').classList.remove('show');

                alert('تم تسجيل الخروج بنجاح');
            }
        }

        // Check login status when page loads
        document.addEventListener('DOMContentLoaded', checkLoginStatus);
    </script>
</body>
</html>
